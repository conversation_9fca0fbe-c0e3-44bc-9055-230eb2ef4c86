import { useEffect, useState } from "react";
import { useAppContext } from "~/lib/providers/app-context";
import {
  useChannels,
  useChannelMessages,
  useChannelMembers,
} from "~/lib/api/client-queries";
import type { Channel } from "stream-chat";

/**
 * Custom hook that provides cached chat data using React Query
 * This helps improve performance by caching channels, messages, and members
 */
export function useChatData() {
  const { chatClient, userId } = useAppContext();
  const [activeChannel, setActiveChannel] = useState<Channel | null>(null);

  // Query for user's channels with caching
  const {
    data: channels = [],
    isLoading: channelsLoading,
    error: channelsError,
    refetch: refetchChannels,
  } = useChannels(chatClient, userId);

  // Query for active channel messages with infinite scroll
  const {
    data: messagesData,
    isLoading: messagesLoading,
    error: messagesError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useChannelMessages(chatClient, activeChannel?.id || null);

  // Query for active channel members
  const {
    data: members = [],
    isLoading: membersLoading,
    error: membersError,
  } = useChannelMembers(chatClient, activeChannel?.id || null);

  // Flatten messages from all pages
  const messages = messagesData?.pages.flatMap((page) => page.messages) || [];

  // Set the first channel as active if none is selected
  useEffect(() => {
    if (channels.length > 0 && !activeChannel) {
      setActiveChannel(channels[0]);
    }
  }, [channels, activeChannel]);

  // Helper function to load more messages
  const loadMoreMessages = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Helper function to refresh all chat data
  const refreshChatData = () => {
    refetchChannels();
  };

  return {
    // Channel data
    channels,
    channelsLoading,
    channelsError,

    // Active channel
    activeChannel,
    setActiveChannel,

    // Messages data
    messages,
    messagesLoading,
    messagesError,
    loadMoreMessages,
    hasMoreMessages: hasNextPage,
    isLoadingMoreMessages: isFetchingNextPage,

    // Members data
    members,
    membersLoading,
    membersError,

    // Utility functions
    refreshChatData,

    // Loading states
    isLoading: channelsLoading || messagesLoading || membersLoading,
    hasError: !!channelsError || !!messagesError || !!membersError,
  };
}

/**
 * Hook for getting cached channel data without subscribing to real-time updates
 * Useful for components that just need to display channel info
 */
export function useCachedChannels() {
  const { chatClient, userId } = useAppContext();

  return useChannels(chatClient, userId);
}

/**
 * Hook for getting cached messages for a specific channel
 * Useful for components that need message history
 */
export function useCachedChannelMessages(channelId: string | null) {
  const { chatClient } = useAppContext();

  return useChannelMessages(chatClient, channelId);
}
