import type { Route } from "./+types/chat";

import "~/styles/chat-layout.css";
import "~/styles/custom-message.css";
import "stream-chat-react/dist/css/v2/index.css";
import {
  generateExpoCSSVariables,
  generateStreamCSSVariables,
  expoTheme,
} from "~/lib/themes/expo-theme";

import { RequireAuth } from "~/lib/auth/session";
import { useAppContext } from "~/lib/providers/app-context";
import { useChatData } from "~/lib/hooks/use-chat-data";
import {
  CustomChannelHeader,
  CustomChannelListHeader,
  CustomChannelPreview,
  CustomMessage,
  CustomMessageInput,
} from "~/components/chat";

import {
  Channel,
  MessageList,
  Thread,
  Chat,
  ChannelList,
  Window,
  MessageTimestamp,
  type MessageTimestampProps,
} from "stream-chat-react";

// Clean chat page - all custom components moved to ~/components/chat/

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Chat - Sphere" },
    { name: "description", content: "Real-time chat messaging" },
  ];
}

const CustomMessageTimestamp = (props: MessageTimestampProps) => (
  <MessageTimestamp {...props} calendar={false} format={"h:mm A"} /> // calendar is enabled by default
);

export default function ChatPage() {
  const { chatClient, userId } = useAppContext();
  const { channels, activeChannel, setActiveChannel, isLoading, hasError } =
    useChatData();

  console.log("userId", userId);
  console.log("chatClient", chatClient);
  console.log("cached channels", channels);

  const filters = { type: "messaging", members: { $in: [userId] } } as any;
  const sort = { last_message_at: -1 } as any;

  // Apply Expo theme CSS variables
  const expoCSSVars = generateExpoCSSVariables();
  const streamCSSVars = generateStreamCSSVariables();

  if (!chatClient) {
    return <div>Loading chat...</div>;
  }

  if (isLoading) {
    return <div>Loading chat data...</div>;
  }

  if (hasError) {
    return <div>Error loading chat. Please try again.</div>;
  }

  return (
    <RequireAuth>
      <div
        className="h-full min-h-screen"
        style={{
          backgroundColor: expoTheme.colors.primary,
          ...expoCSSVars,
          ...streamCSSVars,
        }}
      >
        <Chat
          client={chatClient}
          theme="str-chat__theme-dark"
          // Pass custom components to Chat context
        >
          <div className="flex h-screen">
            {/* Channel List */}
            <div className="channel-list-container">
              <CustomChannelListHeader />
              <ChannelList
                filters={filters}
                sort={sort}
                Preview={(props) => (
                  <CustomChannelPreview
                    {...props}
                    setActiveChannel={setActiveChannel}
                    activeChannel={activeChannel}
                  />
                )}
                options={{
                  state: true,
                  presence: true,
                  limit: 30,
                }}
              />
            </div>

            {/* Main Chat Area */}
            <div className="flex-1 flex">
              {activeChannel ? (
                <Channel
                  channel={activeChannel}
                  MessageTimestamp={CustomMessageTimestamp}
                  Message={CustomMessage}
                >
                  <Window>
                    <CustomChannelHeader />
                    <div className="flex-1 bg-black overflow-hidden">
                      <MessageList noGroupByUser={false} />
                    </div>
                    <CustomMessageInput />
                  </Window>
                  <Thread />
                </Channel>
              ) : (
                <div className="flex-1 flex items-center justify-center bg-black">
                  <div className="text-center text-gray-400">
                    <h3 className="text-lg font-semibold mb-2">
                      No chat selected
                    </h3>
                    <p>
                      Choose a conversation from the sidebar to start chatting
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Chat>
      </div>
    </RequireAuth>
  );
}
